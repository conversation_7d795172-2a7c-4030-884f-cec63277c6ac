import 'base_request.dart';

/// 搜索搭子用户请求
class SearchBuddiesRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.GET;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/api/v1/buddy/search';
  }
}

/// 发送搭子邀请请求
class SendBuddyInvitationRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.POST;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/api/v1/buddy/invitation';
  }
}

/// 获取搭子列表请求
class GetBuddyListRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.GET;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/api/v1/buddy/list';
  }
}

/// 删除搭子关系请求
class RemoveBuddyRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.DELETE;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/api/v1/buddy/relationship';
  }
}

/// 添加搭子关系请求
class AddBuddyRelationshipRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.POST;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/api/v1/buddy/relationship';
  }
}

/// 获取当前用户UID请求
class GetCurrentUserUidRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.GET;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/api/v1/user/current';
  }
}

/// 获取推荐搭子请求
class GetRecommendedBuddiesRequest extends BaseRequest {
  @override
  HttpMethod httpMethod() {
    return HttpMethod.GET;
  }

  @override
  bool needLogin() {
    return true;
  }

  @override
  String path() {
    return '/api/v1/buddy/recommended';
  }
}
