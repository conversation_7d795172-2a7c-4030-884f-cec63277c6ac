import '../../models/buddy_model.dart';
import '../../utils/hi_net.dart';
import '../request/buddy_request.dart';

/// 搭子数据访问层
/// 负责处理搭子相关的数据操作，包括网络请求和本地缓存
class BuddyDao {
  /// 搜索搭子用户
  static Future<BuddySearchResult> searchBuddies({
    required String query,
    int page = 1,
    int pageSize = 10,
  }) async {
    SearchBuddiesRequest request = SearchBuddiesRequest();
    request.add("query", query);
    request.add("page", page);
    request.add("pageSize", pageSize);
    var result = await HiNet.getInstance().fire(request);
    return BuddySearchResult.fromJson(result['data']);
  }

  /// 发送搭子邀请
  static Future<bool> sendBuddyInvitation({
    required String toUserId,
    String? message,
  }) async {
    SendBuddyInvitationRequest request = SendBuddyInvitationRequest();
    request.add("toUserId", toUserId);
    if (message != null) {
      request.add("message", message);
    }
    var result = await HiNet.getInstance().fire(request);
    return result['success'] ?? false;
  }

  /// 获取搭子列表
  static Future<List<BuddyRelationship>> getBuddyList() async {
    GetBuddyListRequest request = GetBuddyListRequest();
    var result = await HiNet.getInstance().fire(request);
    return (result['data'] as List)
        .map((item) => BuddyRelationship.fromJson(item))
        .toList();
  }

  /// 删除搭子关系
  static Future<bool> removeBuddy(String buddyId) async {
    RemoveBuddyRequest request = RemoveBuddyRequest();
    request.add("buddyId", buddyId);
    var result = await HiNet.getInstance().fire(request);
    return result['success'] ?? false;
  }

  /// 添加搭子关系
  static Future<bool> addBuddyRelationship(String buddyId) async {
    AddBuddyRelationshipRequest request = AddBuddyRelationshipRequest();
    request.add("buddyId", buddyId);
    var result = await HiNet.getInstance().fire(request);
    return result['success'] ?? false;
  }

  /// 根据ID获取用户信息
  static Future<BuddyUser?> getUserById(String userId) async {
    GetUserByIdRequest request = GetUserByIdRequest();
    request.add("userId", userId);
    var result = await HiNet.getInstance().fire(request);
    return BuddyUser.fromJson(result['data']);
  }

  /// 获取推荐搭子
  static Future<List<BuddyUser>> getRecommendedBuddies({int limit = 5}) async {
    // TODO: 替换为真实API调用
    GetRecommendedBuddiesRequest request = GetRecommendedBuddiesRequest();
    request.add("limit", limit);
    var result = await HiNet.getInstance().fire(request);
    return (result['data'] as List)
        .map((item) => BuddyUser.fromJson(item))
        .toList();

    // 临时Mock实现
    _initMockData();
    await Future.delayed(const Duration(milliseconds: 400));
    final shuffled = List<BuddyUser>.from(_mockUsers)..shuffle(Random());
    return shuffled.take(limit).toList();
  }
}

/// BuddyRelationship 扩展方法
extension BuddyRelationshipExtension on BuddyRelationship {
  BuddyRelationship copyWith({
    String? id,
    String? userId,
    String? buddyId,
    BuddyUser? buddy,
    DateTime? createdAt,
    bool? isActive,
    int? mutualHabits,
  }) {
    return BuddyRelationship(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      buddyId: buddyId ?? this.buddyId,
      buddy: buddy ?? this.buddy,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      mutualHabits: mutualHabits ?? this.mutualHabits,
    );
  }
}
