import 'dart:math';
import '../../models/buddy_model.dart';
import '../../utils/hi_net.dart';
import '../request/buddy_request.dart';

/// 搭子数据访问层
/// 负责处理搭子相关的数据操作，包括网络请求和本地缓存
class BuddyDao {
  // Mock数据存储 - 后续替换为真实API时可移除
  static final List<BuddyUser> _mockUsers = [];
  static final List<BuddyRelationship> _mockRelationships = [];
  static final List<BuddyInvitation> _mockInvitations = [];

  /// 初始化Mock数据 - 仅用于开发阶段
  static void _initMockData() {
    if (_mockUsers.isNotEmpty) return;

    final mockUserData = [
      {
        'id': '1001',
        'nickname': '晨跑小达人',
        'avatarUrl': 'https://picsum.photos/100/100?random=1',
        'description': '每天坚持晨跑，已经连续100天了！',
        'habitCount': 5,
        'isOnline': true,
      },
      {
        'id': '1002', 
        'nickname': '读书爱好者',
        'avatarUrl': 'https://picsum.photos/100/100?random=2',
        'description': '热爱阅读，目标每年读50本书',
        'habitCount': 3,
        'isOnline': false,
      },
      {
        'id': '1003',
        'nickname': '健身达人',
        'avatarUrl': 'https://picsum.photos/100/100?random=3', 
        'description': '专业健身教练，帮你养成运动习惯',
        'habitCount': 8,
        'isOnline': true,
      },
      {
        'id': '1004',
        'nickname': '早睡早起',
        'avatarUrl': 'https://picsum.photos/100/100?random=4',
        'description': '作息规律，生活健康',
        'habitCount': 4,
        'isOnline': false,
      },
      {
        'id': '1005',
        'nickname': '学习小能手',
        'avatarUrl': 'https://picsum.photos/100/100?random=5',
        'description': '终身学习者，每天进步一点点',
        'habitCount': 6,
        'isOnline': true,
      },
      {
        'id': '1006',
        'nickname': '冥想修行者',
        'avatarUrl': 'https://picsum.photos/100/100?random=6',
        'description': '通过冥想找到内心平静',
        'habitCount': 2,
        'isOnline': false,
      },
      {
        'id': '1007',
        'nickname': '料理研究家',
        'avatarUrl': 'https://picsum.photos/100/100?random=7',
        'description': '热爱烹饪，每天尝试新菜谱',
        'habitCount': 3,
        'isOnline': true,
      },
      {
        'id': '1008',
        'nickname': '写作练习生',
        'avatarUrl': 'https://picsum.photos/100/100?random=8',
        'description': '每天写作1000字，记录生活点滴',
        'habitCount': 4,
        'isOnline': false,
      },
    ];

    _mockUsers.addAll(
      mockUserData.map((data) => BuddyUser.fromJson(data)).toList(),
    );
  }

  /// 搜索搭子用户
  static Future<BuddySearchResult> searchBuddies({
    required String query,
    int page = 1,
    int pageSize = 10,
  }) async {
    // TODO: 替换为真实API调用
    // SearchBuddiesRequest request = SearchBuddiesRequest();
    // request.add("query", query);
    // request.add("page", page);
    // request.add("pageSize", pageSize);
    // var result = await HiNet.getInstance().fire(request);
    // return BuddySearchResult.fromJson(result['data']);

    // 临时Mock实现
    _initMockData();
    await Future.delayed(const Duration(milliseconds: 800));

    List<BuddyUser> filteredUsers;
    if (query.trim().isEmpty) {
      filteredUsers = List.from(_mockUsers);
    } else {
      filteredUsers = _mockUsers.where((user) {
        return user.nickname.toLowerCase().contains(query.toLowerCase()) ||
               user.id.contains(query) ||
               (user.description?.toLowerCase().contains(query.toLowerCase()) ?? false);
      }).toList();
    }

    filteredUsers.shuffle(Random());

    final startIndex = (page - 1) * pageSize;
    final endIndex = startIndex + pageSize;
    final paginatedUsers = filteredUsers.length > startIndex
        ? filteredUsers.sublist(
            startIndex,
            endIndex > filteredUsers.length ? filteredUsers.length : endIndex,
          )
        : <BuddyUser>[];

    return BuddySearchResult(
      users: paginatedUsers,
      total: filteredUsers.length,
      hasMore: endIndex < filteredUsers.length,
      nextCursor: endIndex < filteredUsers.length ? page.toString() : null,
    );
  }

  /// 发送搭子邀请
  static Future<bool> sendBuddyInvitation({
    required String toUserId,
    String? message,
  }) async {
    // TODO: 替换为真实API调用
    // SendBuddyInvitationRequest request = SendBuddyInvitationRequest();
    // request.add("toUserId", toUserId);
    // request.add("message", message);
    // var result = await HiNet.getInstance().fire(request);
    // return result['success'] ?? false;

    // 临时Mock实现
    _initMockData();
    await Future.delayed(const Duration(milliseconds: 500));

    final existingRelation = _mockRelationships.any(
      (r) => r.buddyId == toUserId && r.isActive,
    );

    final existingInvitation = _mockInvitations.any(
      (i) => i.toUserId == toUserId && i.status == BuddyInvitationStatus.pending,
    );

    if (existingRelation || existingInvitation) {
      return false;
    }

    final invitation = BuddyInvitation(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      fromUserId: 'current_user',
      toUserId: toUserId,
      message: message,
      status: BuddyInvitationStatus.pending,
      createdAt: DateTime.now(),
    );

    _mockInvitations.add(invitation);
    return Random().nextDouble() > 0.1;
  }

  /// 获取搭子列表
  static Future<List<BuddyRelationship>> getBuddyList() async {
    // TODO: 替换为真实API调用
    // GetBuddyListRequest request = GetBuddyListRequest();
    // var result = await HiNet.getInstance().fire(request);
    // return (result['data'] as List)
    //     .map((item) => BuddyRelationship.fromJson(item))
    //     .toList();

    // 临时Mock实现
    _initMockData();
    await Future.delayed(const Duration(milliseconds: 300));
    return _mockRelationships.where((r) => r.isActive).toList();
  }

  /// 删除搭子关系
  static Future<bool> removeBuddy(String buddyId) async {
    // TODO: 替换为真实API调用
    // RemoveBuddyRequest request = RemoveBuddyRequest();
    // request.add("buddyId", buddyId);
    // var result = await HiNet.getInstance().fire(request);
    // return result['success'] ?? false;

    // 临时Mock实现
    await Future.delayed(const Duration(milliseconds: 300));
    final relationIndex = _mockRelationships.indexWhere(
      (r) => r.buddyId == buddyId && r.isActive,
    );

    if (relationIndex != -1) {
      _mockRelationships[relationIndex] = _mockRelationships[relationIndex].copyWith(isActive: false);
      return true;
    }
    return false;
  }

  /// 添加搭子关系
  static Future<bool> addBuddyRelationship(String buddyId) async {
    // TODO: 替换为真实API调用
    // AddBuddyRelationshipRequest request = AddBuddyRelationshipRequest();
    // request.add("buddyId", buddyId);
    // var result = await HiNet.getInstance().fire(request);
    // return result['success'] ?? false;

    // 临时Mock实现
    _initMockData();
    await Future.delayed(const Duration(milliseconds: 300));

    final existingRelation = _mockRelationships.any(
      (r) => r.buddyId == buddyId && r.isActive,
    );

    if (existingRelation) {
      return false;
    }

    final buddy = _mockUsers.firstWhere(
      (u) => u.id == buddyId,
      orElse: () => BuddyUser(id: buddyId, nickname: '用户$buddyId'),
    );

    final relationship = BuddyRelationship(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: 'current_user',
      buddyId: buddyId,
      buddy: buddy,
      createdAt: DateTime.now(),
      isActive: true,
      mutualHabits: Random().nextInt(3) + 1,
    );

    _mockRelationships.add(relationship);
    return true;
  }

  /// 根据ID获取用户信息
  static Future<BuddyUser?> getUserById(String userId) async {
    GetUserByIdRequest request = GetUserByIdRequest();
    request.add("userId", userId);
    var result = await HiNet.getInstance().fire(request);
    return BuddyUser.fromJson(result['data']);
  }

  /// 获取推荐搭子
  static Future<List<BuddyUser>> getRecommendedBuddies({int limit = 5}) async {
    // TODO: 替换为真实API调用
    GetRecommendedBuddiesRequest request = GetRecommendedBuddiesRequest();
    request.add("limit", limit);
    var result = await HiNet.getInstance().fire(request);
    return (result['data'] as List)
        .map((item) => BuddyUser.fromJson(item))
        .toList();

    // 临时Mock实现
    _initMockData();
    await Future.delayed(const Duration(milliseconds: 400));
    final shuffled = List<BuddyUser>.from(_mockUsers)..shuffle(Random());
    return shuffled.take(limit).toList();
  }
}

/// BuddyRelationship 扩展方法
extension BuddyRelationshipExtension on BuddyRelationship {
  BuddyRelationship copyWith({
    String? id,
    String? userId,
    String? buddyId,
    BuddyUser? buddy,
    DateTime? createdAt,
    bool? isActive,
    int? mutualHabits,
  }) {
    return BuddyRelationship(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      buddyId: buddyId ?? this.buddyId,
      buddy: buddy ?? this.buddy,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
      mutualHabits: mutualHabits ?? this.mutualHabits,
    );
  }
}
