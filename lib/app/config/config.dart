// 环境配置

class EnvConfig {
  final String appTitle;
  final String appDomain;
  final bool useHttps;
  EnvConfig({
    required this.appTitle,
    required this.appDomain,
    required this.useHttps,
  });
}

// 获取的配置信息
class Env {
  // 获取到当前环境
  static const appEnv = String.fromEnvironment(EnvName.envKey);

  // 开发环境
  static final EnvConfig _devConfig = EnvConfig(
    appTitle: "devTitle",
    appDomain: "192.168.0.200:8001",
    useHttps: false,
  );
  // 发布环境
  static final EnvConfig _releaseConfig = EnvConfig(
    appTitle: "releaseTitle",
    appDomain: "lh-api.dxwvv.com",
    useHttps: true,
  );
  // 测试环境
  static final EnvConfig _testConfig = EnvConfig(
    appTitle: "testTitle",
    appDomain: "121.5.140.181:30003",
    useHttps: false,
  );

  static EnvConfig get envConfig => _getEnvConfig();

// 根据不同环境返回对应的环境配置
  static EnvConfig _getEnvConfig() {
    switch (appEnv) {
      case EnvName.dev:
        return _devConfig;
      case EnvName.release:
        return _releaseConfig;
      case EnvName.test:
        return _testConfig;
      default:
        return _devConfig;
    }
  }
}

// 声明的环境
abstract class EnvName {
  // 环境key
  static const String envKey = "DART_DEFINE_APP_ENV";
  // 环境value
  static const String dev = "dev";
  static const String release = "release";
  static const String test = "test";
}
