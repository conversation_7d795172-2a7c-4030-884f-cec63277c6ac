import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:life_habit_app/app/theme/app_colors.dart';

class ToRefresh extends StatefulWidget {
  final Function()? onRefresh;
  final String? title;
  final String? subtitle;
  final IconData? icon;
  final String? buttonText;

  const ToRefresh({
    super.key, 
    this.onRefresh,
    this.title,
    this.subtitle,
    this.icon,
    this.buttonText,
  });

  @override
  State<ToRefresh> createState() => _ToRefreshState();
}

class _ToRefreshState extends State<ToRefresh> 
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 48),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 图标容器 - 添加渐变和阴影
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.grey.shade50,
                          Colors.grey.shade100,
                        ],
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.shade300.withOpacity(0.5),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                          spreadRadius: 0,
                        ),
                        BoxShadow(
                          color: Colors.white.withOpacity(0.8),
                          blurRadius: 20,
                          offset: const Offset(0, -4),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Icon(
                      widget.icon ?? Icons.wifi_off_rounded,
                      size: 36,
                      color: Colors.grey.shade500,
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 副标题 - 优化间距和颜色
                  Container(
                    constraints: const BoxConstraints(maxWidth: 280),
                    child: Text(
                      widget.subtitle ?? _getSubtitle(),
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.grey.shade600,
                        height: 1.5,
                        letterSpacing: 0.1,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // 重试按钮 - 添加渐变和更好的阴影
                  Container(
                    width: double.infinity,
                    height: 52,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.3),
                          blurRadius: 16,
                          offset: const Offset(0, 2),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: ElevatedButton.icon(
                      onPressed: widget.onRefresh,
                      icon: Container(
                        padding: const EdgeInsets.all(2),
                        child: Icon(
                          Icons.refresh_rounded,
                          size: 20,
                          color: Colors.white,
                        ),
                      ),
                      label: Text(
                        widget.buttonText ?? _getButtonText(),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        elevation: 0,
                        shadowColor: Colors.transparent,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 16,
                        ),
                      ).copyWith(
                        overlayColor: WidgetStateProperty.resolveWith<Color?>(
                          (Set<WidgetState> states) {
                            if (states.contains(WidgetState.pressed)) {
                              return Colors.white.withOpacity(0.1);
                            }
                            if (states.contains(WidgetState.hovered)) {
                              return Colors.white.withOpacity(0.05);
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // 添加一个微妙的装饰元素
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  String _getTitle() {
    final locale = Get.locale?.languageCode ?? 'en';
    if (locale == 'zh') {
      return '网络连接问题';
    } else {
      return 'Connection Problem';
    }
  }

  String _getSubtitle() {
    final locale = Get.locale?.languageCode ?? 'en';
    if (locale == 'zh') {
      return '请检查网络后重试';
    } else {
      return 'Please check your connection and try again';
    }
  }

  String _getButtonText() {
    final locale = Get.locale?.languageCode ?? 'en';
    if (locale == 'zh') {
      return '重试';
    } else {
      return 'Try Again';
    }
  }
}
