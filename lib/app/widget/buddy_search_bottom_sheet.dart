import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/buddy_model.dart';
import '../data/dao/buddy_dao.dart';
import '../theme/app_colors.dart';
import '../utils/customFonts.dart';
import '../utils/toast.dart';
import '../utils/hi_constants.dart';

class BuddySearchBottomSheet extends StatefulWidget {
  final Function(String buddyId)? onBuddySelected;

  const BuddySearchBottomSheet({
    super.key,
    this.onBuddySelected,
  });

  @override
  State<BuddySearchBottomSheet> createState() => _BuddySearchBottomSheetState();
}

class _BuddySearchBottomSheetState extends State<BuddySearchBottomSheet> {
  final TextEditingController _searchController = TextEditingController();

  final RxList<BuddyUser> _searchResults = <BuddyUser>[].obs;
  final RxBool _isLoading = false.obs;
  final RxBool _hasSearched = false.obs;
  final RxString _errorMessage = ''.obs;
  final RxList<String> _addingBuddies = <String>[].obs; // 正在添加的搭子ID列表
  final RxString _searchText = ''.obs; // 用于监听搜索框文本变化
  final RxString _currentUserUid = ''.obs; // 当前用户的UID

  @override
  void initState() {
    super.initState();
    _loadCurrentUserUid();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 加载当前用户的UID
  Future<void> _loadCurrentUserUid() async {
    try {
      final uid = await BuddyDao.getCurrentUserUid();
      _currentUserUid.value = uid;
    } catch (e) {
      // 获取失败时不影响其他功能
      _currentUserUid.value = '';
    }
  }

  /// 搜索搭子
  Future<void> _searchBuddies(String query) async {
    if (query.trim().isEmpty) {
      _searchResults.clear();
      _hasSearched.value = false;
      _errorMessage.value = '';
      return;
    }

    _isLoading.value = true;
    _hasSearched.value = true;
    _errorMessage.value = '';

    try {
      final result = await BuddyDao.searchBuddiesByUid(uid: query.trim());
      _searchResults.value = result.users;

      if (result.users.isEmpty) {
        _errorMessage.value = '未找到相关用户'.tr;
      }
    } catch (e) {
      _errorMessage.value = '搜索失败，请重试'.tr;
      _searchResults.clear();
    } finally {
      _isLoading.value = false;
    }
  }

  /// 添加搭子
  Future<void> _addBuddy(BuddyUser user) async {
    if (_addingBuddies.contains(user.id)) return;

    _addingBuddies.add(user.id);

    try {
      final success = await BuddyDao.sendBuddyInvitation(
        toUserId: user.id,
        message: '希望我们能一起养成好习惯！',
      );

      if (success) {
        Get.snackbar(
          '邀请成功'.tr,
          '邀请已发送给 ${user.nickname}'.tr,
          backgroundColor: AppColors.primary,
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
        widget.onBuddySelected?.call(user.id);
        Get.back(); // 关闭弹窗
      } else {
        showErrorMessage(HiConstants.errorTypeOther, '发送邀请失败，可能已存在关系'.tr);
      }
    } catch (e) {
      showErrorMessage(HiConstants.errorTypeOther, '发送邀请失败，请重试'.tr);
    } finally {
      _addingBuddies.remove(user.id);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: MediaQuery.of(context).size.height * 0.4,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 头部
          _buildHeader(),

          // 搜索栏
          _buildSearchBar(),

          // 可滚动内容区域
          Flexible(
            child: SingleChildScrollView(
              physics: const ClampingScrollPhysics(),
              child: _buildContent(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 24, 20, 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border(
          bottom: BorderSide(color: const Color(0xFFF5F5F5), width: 1),
        ),
      ),
      child: Row(
        children: [
          // 关闭按钮
          GestureDetector(
            onTap: () => Get.back(),
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: const Color(0xFFF8F8F8),
                shape: BoxShape.circle,
                border: Border.all(color: const Color(0xFFE8E8E8), width: 1),
              ),
              child: Icon(
                Icons.close_rounded,
                size: 20,
                color: Colors.grey.shade600,
              ),
            ),
          ),
          const SizedBox(width: 16),
          // 标题区域
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '寻找习惯搭子'.tr,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '找到志同道合的伙伴，一起坚持好习惯'.tr,
                  style: const TextStyle(
                    fontSize: 13,
                    color: Color(0xFF666666),
                    height: 1.3,
                  ),
                ),
                const SizedBox(height: 8),
                // 显示当前用户UID
                Obx(() => _currentUserUid.value.isNotEmpty
                    ? Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF8F9FA),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                              color: const Color(0xFFE9ECEF), width: 1),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.person_outline,
                              size: 14,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '我的UID: ${_currentUserUid.value}'.tr,
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 4),
                            GestureDetector(
                              onTap: () {
                                // TODO: 复制UID到剪贴板
                                // Clipboard.setData(ClipboardData(text: _currentUserUid.value));
                                // Get.snackbar('已复制', 'UID已复制到剪贴板');
                              },
                              child: Icon(
                                Icons.copy,
                                size: 12,
                                color: Colors.grey.shade500,
                              ),
                            ),
                          ],
                        ),
                      )
                    : const SizedBox.shrink()),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          border: Border.all(color: const Color(0xFFE8E8E8), width: 1.5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: TextField(
          controller: _searchController,
          onChanged: (value) {
            _searchText.value = value;
            // 防抖搜索
            Future.delayed(const Duration(milliseconds: 500), () {
              if (_searchController.text == value) {
                _searchBuddies(value);
              }
            });
          },
          decoration: InputDecoration(
            hintText: '输入用户UID'.tr,
            hintStyle: const TextStyle(
              color: Color(0xFF999999),
              fontSize: 15,
            ),
            prefixIcon: Container(
              padding: const EdgeInsets.all(12),
              child: Icon(
                Icons.search_rounded,
                color: AppColors.primary.withOpacity(0.6),
                size: 22,
              ),
            ),
            suffixIcon: Obx(() => _searchText.value.isNotEmpty
                ? GestureDetector(
                    onTap: () {
                      _searchController.clear();
                      _searchText.value = '';
                      _searchBuddies('');
                    },
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      child: Icon(
                        Icons.clear_rounded,
                        color: Colors.grey.shade400,
                        size: 20,
                      ),
                    ),
                  )
                : const SizedBox.shrink()),
            border: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 14,
            ),
          ),
          style: const TextStyle(
            fontSize: 15,
            color: Color(0xFF333333),
          ),
        ),
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Obx(() {
      if (_isLoading.value) {
        return _buildLoadingState();
      }

      if (_errorMessage.value.isNotEmpty) {
        return _buildErrorState();
      }

      if (_searchResults.isEmpty) {
        return _buildEmptyState();
      }

      return _buildUserList();
    });
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primary,
            strokeWidth: 2,
          ),
          SizedBox(height: 16),
          Text(
            '搜索中...',
            style: TextStyle(
              color: Color(0xFF666666),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage.value,
            style: const TextStyle(
              color: Color(0xFF666666),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          TextButton(
            onPressed: () {
              if (_hasSearched.value && _searchController.text.isNotEmpty) {
                _searchBuddies(_searchController.text);
              } else {
                _errorMessage.value = '';
              }
            },
            child: Text(
              '重试'.tr,
              style: const TextStyle(
                color: AppColors.primary,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    if (_hasSearched.value) {
      // 搜索无结果状态
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.primary.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.search_off_rounded,
                size: 60,
                color: AppColors.primary.withOpacity(0.6),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '未找到相关用户'.tr,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '试试其他关键词或邀请朋友加入'.tr,
              style: const TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
              ),
            ),
          ],
        ),
      );
    }

    // 默认占位符状态
    return _buildPlaceholderState();
  }

  /// 构建占位符状态
  Widget _buildPlaceholderState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 主要插图
          Container(
            width: 140,
            height: 140,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primary.withOpacity(0.1),
                  AppColors.primary.withOpacity(0.05),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // 背景圆圈
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withOpacity(0.15),
                    shape: BoxShape.circle,
                  ),
                ),
                // 主图标
                Icon(
                  Icons.people_alt_rounded,
                  size: 40,
                  color: AppColors.primary,
                ),
                // 装饰性小图标
                Positioned(
                  top: 15,
                  right: 15,
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.star_rounded,
                      size: 12,
                      color: Colors.orange,
                    ),
                  ),
                ),
                Positioned(
                  bottom: 20,
                  left: 10,
                  child: Container(
                    width: 18,
                    height: 18,
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.favorite_rounded,
                      size: 10,
                      color: Colors.green,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建用户列表
  Widget _buildUserList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 列表标题
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              Text(
                '搜索结果'.tr,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF333333),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${_searchResults.length}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ),
            ],
          ),
        ),
        // 用户列表
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: _searchResults.length,
          itemBuilder: (context, index) {
            final user = _searchResults[index];
            return _buildUserCard(user);
          },
        ),
      ],
    );
  }

  /// 构建用户卡片
  Widget _buildUserCard(BuddyUser user) {
    return Obx(() {
      final isAdding = _addingBuddies.contains(user.id);

      return Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: const Color(0xFFE8E8E8), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.06),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // 头像
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.grey.shade100,
                border: Border.all(
                  color: AppColors.primary.withOpacity(0.1),
                  width: 2,
                ),
                image: user.avatarUrl != null
                    ? DecorationImage(
                        image: NetworkImage(user.avatarUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: user.avatarUrl == null
                  ? Icon(
                      Icons.person_rounded,
                      color: AppColors.primary.withOpacity(0.6),
                      size: 28,
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            // 用户信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        user.nickname,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF333333),
                        ),
                      ),
                      const SizedBox(width: 8),
                      if (user.isOnline)
                        Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Color(0xFF4CAF50),
                            shape: BoxShape.circle,
                          ),
                        ),
                    ],
                  ),
                  if (user.description != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      user.description!,
                      style: const TextStyle(
                        fontSize: 13,
                        color: Color(0xFF666666),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      Icon(
                        CustomFonts.mutilStar,
                        size: 12,
                        color: Colors.grey.shade500,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${user.habitCount ?? 0} 个习惯',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            // 添加按钮
            GestureDetector(
              onTap: isAdding ? null : () => _addBuddy(user),
              child: Container(
                width: 80,
                height: 36,
                decoration: BoxDecoration(
                  gradient: isAdding
                      ? null
                      : LinearGradient(
                          colors: [
                            AppColors.primary,
                            AppColors.primary.withOpacity(0.8)
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                  color: isAdding ? Colors.grey.shade200 : null,
                  borderRadius: BorderRadius.circular(18),
                  boxShadow: isAdding
                      ? null
                      : [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                ),
                child: isAdding
                    ? const Center(
                        child: SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            color: Colors.grey,
                            strokeWidth: 2,
                          ),
                        ),
                      )
                    : const Center(
                        child: Text(
                          '添加',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
              ),
            ),
          ],
        ),
      );
    });
  }
}
