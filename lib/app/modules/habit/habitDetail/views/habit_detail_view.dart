import 'dart:io';

import 'package:dashed_circular_progress_bar/dashed_circular_progress_bar.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:life_habit_app/app/components/keep_alive_wrapper.dart';

import 'package:get/get.dart';
import 'package:life_habit_app/app/models/user_habit_snapshot_detail_model.dart';
import 'package:life_habit_app/app/modules/home/<USER>/home_controller.dart';
import 'package:life_habit_app/app/routes/app_pages.dart';
import 'package:life_habit_app/app/utils/bottom_sheet.dart';
import 'package:life_habit_app/app/utils/color.dart';
import 'package:life_habit_app/app/utils/customFonts.dart';
import 'package:life_habit_app/app/utils/enums.dart';
import 'package:life_habit_app/app/utils/hi_net.dart';
import 'package:life_habit_app/app/utils/screenAdapter.dart';
import 'package:life_habit_app/app/utils/toast.dart';
import 'package:life_habit_app/app/widget/custom_calendar.dart';
import 'package:scrollable_clean_calendar/models/day_values_model.dart';
import 'package:scrollable_clean_calendar/scrollable_clean_calendar.dart';
import 'package:scrollable_clean_calendar/utils/enums.dart';

import '../../../../theme/app_colors.dart';
import '../../../../utils/hi_constants.dart';
import '../../../../widget/custom_note_dialog.dart';
import '../controllers/habit_detail_controller.dart';
import 'habit_action_card.dart';

class HabitDetailView extends GetView<HabitDetailController> {
  const HabitDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      backgroundColor: Colors.white,
      body: _buildBody(context),
    );
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        padding: const EdgeInsets.only(left: 10),
        icon: const Icon(Icons.arrow_back, color: Color(0xFF212121), size: 20),
        onPressed: () {
          if (Get.isRegistered<HomeController>()) {
            HomeController homeController = Get.find<HomeController>();
            homeController.refreshHabitData();
          }

          Get.back();
        },
      ),
      titleSpacing: 6,
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Obx(() {
            return Expanded(
              child: Text(
                controller.userHabitSnapshotDetailItemModel.value.name ?? "",
                style: TextStyle(
                  color: Color(0xFF464646),
                  fontSize: 20,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
              ),
            );
          }),
          IconButton(
            padding: EdgeInsets.only(right: 10),
            onPressed: () {
              Get.toNamed(Routes.HABIT_EDIT, arguments: {
                "userHabitID": controller.userHabitID,
                "userHabitSnapshotDetailItemModel":
                    controller.userHabitDetailItemModel
              });
            },
            icon: Icon(CustomFonts.edit, color: Color(0xFF4B5563), size: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return CustomScrollView(
      physics: const ClampingScrollPhysics(),
      slivers: [
        SliverPadding(
          padding: EdgeInsets.only(
              left: ScreenAdapter.width(defaultMargin),
              right: ScreenAdapter.width(defaultMargin),
              top: ScreenAdapter.height(defaultMargin)),
          sliver: SliverList(
            delegate: SliverChildListDelegate([
              KeepAliveWrapper(child: _buildCalendar(context)),
              KeepAliveWrapper(child: _buildHabitOperate(context)),
              KeepAliveWrapper(child: _buildHabitStatistic(context)),
              KeepAliveWrapper(child: _buildHabitTimeline(context)),
              SizedBox(height: ScreenAdapter.height(150)),
            ]),
          ),
        ),
      ],
    );
  }

  // 使用缓存减少日历重建次数
  Widget _buildCalendar(BuildContext context) {
    // 预先获取 locale，避免在 build 中重复计算
    final fullLocale = Localizations.localeOf(context).toString();
    String locale = fullLocale.startsWith('zh') ? 'zh' : 'en';

    return RepaintBoundary(
      child: Container(
        height: ScreenAdapter.height(790),
        padding: EdgeInsets.symmetric(horizontal: ScreenAdapter.width(40)),
        decoration: BoxDecoration(
          color: Color(0xFFF8FBFA),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Color(0xFFE5E7EB)),
          // 移除阴影提高性能
        ),
        // 使用 RepaintBoundary 而不是 Obx，完全隔离绘制区域
        child: RepaintBoundary(
          child: ScrollableCleanCalendar(
            locale: locale,
            calendarController: controller.calendarController,
            layout: Layout.DEFAULT,
            spaceBetweenMonthAndCalendar: 0,
            spaceBetweenCalendars: ScreenAdapter.height(15),
            calendarCrossAxisSpacing: 10,
            calendarMainAxisSpacing: 0,
            monthTextStyle: TextStyle(
                fontSize: ScreenAdapter.fontSize(48),
                fontWeight: FontWeight.w500,
                color: Color(0xFF1F2937)),
            padding: EdgeInsets.zero,
            // 使用 RepaintBoundary 包装构建器以隔离绘制
            weekdayBuilder: (context, weekday) => RepaintBoundary(
              child: Container(
                margin: EdgeInsets.only(bottom: 8),
                alignment: Alignment.bottomCenter,
                child: Text(weekday,
                    style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(40),
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF6B7280),
                    )),
              ),
            ),
            dayBuilder: (
              BuildContext context,
              DayValues values,
            ) {
              return RepaintBoundary(
                child: customCalendarDay(
                  context,
                  values,
                  primaryColor,
                  Color(0xFFF8FBFA),
                  primaryColor.withOpacity(0.3),
                  Color(0xFFF8FBFA),
                  Color(0xFF9CA3AF),
                  6,
                  TextStyle(fontSize: ScreenAdapter.fontSize(41)),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildHabitOperate(BuildContext context) {
    // 使用新的HabitActionCard组件替换原有的操作区域
    return HabitActionCard(
      controller: controller,
      context: context,
      isCompleted:
          (Get.arguments != null && Get.arguments.containsKey("isCompleted"))
              ? Get.arguments["isCompleted"]
              : false,
    );
  }

  Widget _buildHabitStatistic(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(1080),
      height: ScreenAdapter.height(450),
      decoration: BoxDecoration(
        color: Color(0xFFF8FBFA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Color(0xFFE5E7EB)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() {
        return Column(
          children: [
            _buildHabitStatisticTitle(context),
            if (controller.selectLabel.value == habitDetailLabelAll)
              Expanded(child: _buildAllStatistic(context)),
            if (controller.selectLabel.value == habitDetailLabelWeek)
              Expanded(child: _buildWeekStatistic(context)),
            if (controller.selectLabel.value == habitDetailLabelMonth)
              Expanded(child: _buildMonthStatistic(context)),
          ],
        );
      }),
    );
  }

  Widget _buildHabitStatisticTitle(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Padding(
          padding: EdgeInsets.only(left: ScreenAdapter.width(40)),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text("统计".tr,
                  style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(46),
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF1F2937))),
            ],
          ),
        ),
        Obx(
          () => Padding(
            padding: EdgeInsets.only(right: ScreenAdapter.width(20)),
            child: Wrap(
              spacing: ScreenAdapter.height(10),
              alignment: WrapAlignment.start,
              direction: Axis.horizontal,
              children: controller.dateFilterLabelList
                  .map((element) => ActionChip(
                        label: Text(
                          habitDetailLabelMap[element]!.tr,
                        ),
                        labelStyle: TextStyle(
                            color: controller.selectLabel.value == element
                                ? Colors.white
                                : Colors.black,
                            fontSize: ScreenAdapter.fontSize(35)),
                        onPressed: () async {
                          controller.selectLabel.value = element;
                          try {
                            await commonPerformRequest(
                                requestFunction: () =>
                                    controller.getHabitStatisticByDateData());
                          } catch (e) {}
                        },
                        backgroundColor: controller.selectLabel.value == element
                            ? primaryColor
                            : bgButtonColor,
                        side: BorderSide.none,
                        shadowColor: primaryColor,
                        elevation: 0.7,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8)),
                      ))
                  .toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAllStatistic(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: ScreenAdapter.height(40)),
      child: Obx(() {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                SizedBox(
                  width: ScreenAdapter.width(200),
                  height: ScreenAdapter.height(200),
                  child: DashedCircularProgressBar.aspectRatio(
                    aspectRatio: 1,
                    valueNotifier: controller.allValueNotifier,
                    progress: controller.habitStatisticAllData.value.per
                            ?.toDouble() ??
                        0,
                    startAngle: 225,
                    sweepAngle: 270,
                    foregroundColor: primaryColor,
                    backgroundColor: const Color(0xFFE2F4EC),
                    foregroundStrokeWidth: 6,
                    backgroundStrokeWidth: 5,
                    animation: true,
                    seekSize: 0,
                    child: Center(
                      child: ValueListenableBuilder(
                          valueListenable: controller.allValueNotifier,
                          builder: (_, double value, __) => Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    '${value.toInt()}%',
                                    style: TextStyle(
                                      color: Colors.black,
                                      fontWeight: FontWeight.w600,
                                      fontSize: ScreenAdapter.fontSize(40),
                                    ),
                                  ),
                                  Text(
                                    '${controller.habitStatisticAllData.value.doneCount ?? 0}/${controller.habitStatisticAllData.value.allCount ?? 0}',
                                    style: TextStyle(
                                      color: primaryColor,
                                      fontWeight: FontWeight.w400,
                                      fontSize: ScreenAdapter.fontSize(36),
                                    ),
                                  ),
                                ],
                              )),
                    ),
                  ),
                ),
                Text(
                  "完成率".tr,
                  style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(39),
                    fontWeight: FontWeight.w600,
                    color: Colors.black54,
                  ),
                )
              ],
            ),
            ..._buildAllStatisticOtherContent(context),
          ],
        );
      }),
    );
  }

  List<Widget> _buildAllStatisticOtherContent(BuildContext context) {
    if (controller.userHabitSnapshotDetailItemModel.value.habitType ==
        habitTypeRecord) {
      var firstText = "平均时间";
      var secondText = "最早时间";
      var thirdText = "最晚时间";
      if (controller
              .userHabitSnapshotDetailItemModel.value.config!.recordType ==
          recordTypeDuration) {
        firstText = "平均时长";
        secondText = "最短时长";
        thirdText = "最长时长";
      } else if (controller
              .userHabitSnapshotDetailItemModel.value.config!.recordType ==
          recordTypeCount) {
        firstText = "平均次数";
        secondText = "最少次数";
        thirdText = "最多次数";
      }
      return [
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              controller.habitStatisticAllData.value.avgData ?? "",
              style: TextStyle(
                color: primaryColor,
                fontSize: ScreenAdapter.fontSize(60),
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: ScreenAdapter.height(60)),
            Text(
              firstText.tr,
              style: TextStyle(
                fontSize: ScreenAdapter.fontSize(39),
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              controller.habitStatisticAllData.value.minData ?? "",
              style: TextStyle(
                color: primaryColor,
                fontSize: ScreenAdapter.fontSize(60),
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: ScreenAdapter.height(60)),
            Text(
              secondText.tr,
              style: TextStyle(
                fontSize: ScreenAdapter.fontSize(39),
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              controller.habitStatisticAllData.value.maxData ?? "",
              style: TextStyle(
                color: primaryColor,
                fontSize: ScreenAdapter.fontSize(60),
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: ScreenAdapter.height(60)),
            Text(
              thirdText.tr,
              style: TextStyle(
                fontSize: ScreenAdapter.fontSize(39),
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ];
    } else {
      return [
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Expanded(
              child: Center(
                child: RichText(
                  text: TextSpan(
                      style: TextStyle(
                          fontSize: ScreenAdapter.fontSize(40),
                          fontWeight: FontWeight.w600),
                      children: [
                        TextSpan(
                            text:
                                '${controller.habitStatisticAllData.value.persistLongDays ?? 0}',
                            style: TextStyle(
                                color: primaryColor,
                                fontSize: ScreenAdapter.fontSize(60))),
                        TextSpan(
                            text: " ${"天".tr}",
                            style: TextStyle(color: Colors.black87))
                      ]),
                ),
              ),
            ),
            Text(
              "最长连续".tr,
              style: TextStyle(
                fontSize: ScreenAdapter.fontSize(39),
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: EdgeInsets.only(bottom: ScreenAdapter.height(10)),
                    child: Text(
                        controller
                            .habitStatisticAllData.value.persistStartDate!,
                        style: TextStyle(
                            color: primaryColor,
                            fontSize: ScreenAdapter.fontSize(40),
                            fontWeight: FontWeight.w500)),
                  ),
                  Text(controller.habitStatisticAllData.value.persistEndDate!,
                      style: TextStyle(
                          color: primaryColor,
                          fontSize: ScreenAdapter.fontSize(40),
                          fontWeight: FontWeight.w500)),
                ],
              ),
            ),
            Text(
              "连续时间段".tr,
              style: TextStyle(
                fontSize: ScreenAdapter.fontSize(39),
                color: Colors.black54,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ];
    }
  }

  Widget _buildWeekStatistic(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: ScreenAdapter.height(10)),
      child: Obx(() {
        return Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: ScreenAdapter.width(500),
              height: ScreenAdapter.height(300),
              child: BarChart(
                BarChartData(
                  maxY: controller.habitStatisticWeekData.value.chartLeftCount!
                          .isNotEmpty
                      ? controller
                          .habitStatisticWeekData.value.chartLeftCount!.last
                          .toDouble()
                      : 20,
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: weekBottomTitles,
                        reservedSize: 42,
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: 1,
                        getTitlesWidget: weekLeftTitles,
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: false,
                  ),
                  barGroups: controller.showingBarGroups,
                  gridData: const FlGridData(show: false),
                ),
              ),
            ),
            _buildWeekStatisticOtherContent(context),
          ],
        );
      }),
    );
  }

  Widget _buildWeekStatisticOtherContent(BuildContext context) {
    if (controller.userHabitSnapshotDetailItemModel.value.habitType ==
        habitTypeRecord) {
      var showText = "平均时间";
      if (controller
              .userHabitSnapshotDetailItemModel.value.config!.recordType ==
          recordTypeDuration) {
        showText = "平均时长";
      } else if (controller
              .userHabitSnapshotDetailItemModel.value.config!.recordType ==
          recordTypeCount) {
        showText = "平均次数";
      }
      return Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: ScreenAdapter.width(5),
            right: ScreenAdapter.width(5),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    controller.habitStatisticWeekData.value.avgData ?? "0",
                    style: TextStyle(
                      color: primaryColor,
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(50),
                    ),
                  ),
                  SizedBox(height: ScreenAdapter.height(60)),
                  Text(
                    showText.tr,
                    style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(35),
                      color: Colors.black54,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    } else {
      return Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: ScreenAdapter.width(5),
            right: ScreenAdapter.width(5),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${controller.habitStatisticWeekData.value.doneCount ?? 0}/${controller.habitStatisticWeekData.value.allCount ?? 0}',
                    style: TextStyle(
                      color: primaryColor,
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(50),
                    ),
                  ),
                  SizedBox(height: ScreenAdapter.height(60)),
                  Text(
                    "完成率".tr,
                    style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(35),
                      color: Colors.black54,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              if (controller
                          .userHabitSnapshotDetailItemModel.value.habitType! ==
                      habitTypeSmall &&
                  controller.habitStatisticWeekData.value.stages!.length > 1)
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    RichText(
                      text: TextSpan(
                          style: TextStyle(
                              fontSize: ScreenAdapter.fontSize(34),
                              fontWeight: FontWeight.w500),
                          children: [
                            TextSpan(
                                text:
                                    '${controller.habitStatisticWeekData.value.stages?[0] ?? 0}',
                                style: TextStyle(
                                    color: primaryColor,
                                    fontSize: ScreenAdapter.fontSize(52))),
                            TextSpan(
                                text: " ${"次".tr}",
                                style: TextStyle(color: Colors.black87))
                          ]),
                    ),
                    SizedBox(height: ScreenAdapter.height(60)),
                    Text(
                      "阶段一".tr,
                      style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(35),
                        color: controller.leftBarColor.withOpacity(0.7),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              if (controller
                          .userHabitSnapshotDetailItemModel.value.habitType! ==
                      habitTypeSmall &&
                  controller.habitStatisticWeekData.value.stages!.length > 1)
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    RichText(
                      text: TextSpan(
                          style: TextStyle(
                              fontSize: ScreenAdapter.fontSize(34),
                              fontWeight: FontWeight.w500),
                          children: [
                            TextSpan(
                                text:
                                    '${controller.habitStatisticWeekData.value.stages?[1] ?? 0}',
                                style: TextStyle(
                                    color: primaryColor,
                                    fontSize: ScreenAdapter.fontSize(52))),
                            TextSpan(
                                text: " ${"次".tr}",
                                style: TextStyle(color: Colors.black87))
                          ]),
                    ),
                    SizedBox(height: ScreenAdapter.height(60)),
                    Text(
                      "阶段二".tr,
                      style: TextStyle(
                        fontSize: ScreenAdapter.fontSize(35),
                        color: controller.rightBarColor.withOpacity(0.7),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildMonthStatistic(BuildContext context) {
    var maxYValue = 6.0;
    if (controller.habitStatisticMonthData.value.chartLeftCount != null &&
        controller.habitStatisticMonthData.value.chartLeftCount!.isNotEmpty) {
      // 如果不为空，则返回最后一个元素的值
      maxYValue = controller.habitStatisticMonthData.value.chartLeftCount!.last
          .toDouble();
    }

    return Padding(
      padding: EdgeInsets.only(top: ScreenAdapter.height(10)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: ScreenAdapter.width(500),
            height: ScreenAdapter.height(300),
            child: Obx(() {
              return LineChart(
                LineChartData(
                  lineTouchData: LineTouchData(
                    handleBuiltInTouches: true,
                    touchTooltipData: LineTouchTooltipData(
                      getTooltipColor: (touchedSpot) =>
                          Colors.blueGrey.withOpacity(0.8),
                    ),
                  ),
                  gridData: const FlGridData(show: false),
                  titlesData: monthTitlesData(),
                  borderData: FlBorderData(
                    show: true,
                    border: const Border(
                      bottom: BorderSide(color: Colors.transparent),
                      left: BorderSide(color: Color(0xFFF7CA8F), width: 1.5),
                      right: BorderSide(color: Colors.transparent),
                      top: BorderSide(color: Colors.transparent),
                    ),
                  ),
                  lineBarsData: [
                    LineChartBarData(
                      isCurved: true,
                      curveSmoothness: 0,
                      color: Color(0xFFF7CA8F),
                      barWidth: 1.5,
                      isStrokeCapRound: true,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(show: false),
                      spots: controller.showingMonthSpots,
                    )
                  ],
                  minX: 1,
                  maxX: 31,
                  maxY: maxYValue,
                  minY: 0,
                ),
                duration: const Duration(milliseconds: 250),
              );
            }),
          ),
          _buildMonthStatisticOtherContent(context),
        ],
      ),
    );
  }

  Widget _buildMonthStatisticOtherContent(BuildContext context) {
    if (controller.userHabitSnapshotDetailItemModel.value.habitType ==
        habitTypeRecord) {
      var showText = "平均时间";
      if (controller
              .userHabitSnapshotDetailItemModel.value.config!.recordType ==
          recordTypeDuration) {
        showText = "平均时长";
      } else if (controller
              .userHabitSnapshotDetailItemModel.value.config!.recordType ==
          recordTypeCount) {
        showText = "平均次数";
      }
      return Expanded(
        child: Padding(
          padding: EdgeInsets.only(
            left: ScreenAdapter.width(5),
            right: ScreenAdapter.width(5),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    controller.habitStatisticMonthData.value.avgData ?? "0",
                    style: TextStyle(
                      color: primaryColor,
                      fontWeight: FontWeight.w500,
                      fontSize: ScreenAdapter.fontSize(50),
                    ),
                  ),
                  SizedBox(height: ScreenAdapter.height(60)),
                  Text(
                    showText.tr,
                    style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(35),
                      color: Colors.black54,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    } else {
      return Expanded(
          child: Padding(
              padding: EdgeInsets.only(
                left: ScreenAdapter.width(5),
                right: ScreenAdapter.width(5),
              ),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${controller.habitStatisticMonthData.value.doneCount ?? 0}/${controller.habitStatisticMonthData.value.allCount ?? 0}',
                          style: TextStyle(
                            color: primaryColor,
                            fontWeight: FontWeight.w500,
                            fontSize: ScreenAdapter.fontSize(50),
                          ),
                        ),
                        SizedBox(height: ScreenAdapter.height(60)),
                        Text(
                          "完成率".tr,
                          style: TextStyle(
                            fontSize: ScreenAdapter.fontSize(35),
                            color: Colors.black54,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    if (controller.userHabitSnapshotDetailItemModel.value
                                .habitType! ==
                            habitTypeSmall &&
                        controller
                                .habitStatisticMonthData.value.stages!.length >
                            1)
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          RichText(
                            text: TextSpan(
                                style: TextStyle(
                                    fontSize: ScreenAdapter.fontSize(34),
                                    fontWeight: FontWeight.w500),
                                children: [
                                  TextSpan(
                                      text:
                                          '${controller.habitStatisticMonthData.value.stages?[0] ?? 0}',
                                      style: TextStyle(
                                          color: primaryColor,
                                          fontSize:
                                              ScreenAdapter.fontSize(52))),
                                  TextSpan(
                                      text: " ${"次".tr}",
                                      style: TextStyle(color: Colors.black87))
                                ]),
                          ),
                          SizedBox(height: ScreenAdapter.height(60)),
                          Text(
                            "阶段一".tr,
                            style: TextStyle(
                              fontSize: ScreenAdapter.fontSize(35),
                              color: Colors.black54,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    if (controller.userHabitSnapshotDetailItemModel.value
                                .habitType! ==
                            habitTypeSmall &&
                        controller
                                .habitStatisticMonthData.value.stages!.length >
                            1)
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          RichText(
                            text: TextSpan(
                                style: TextStyle(
                                    fontSize: ScreenAdapter.fontSize(34),
                                    fontWeight: FontWeight.w500),
                                children: [
                                  TextSpan(
                                      text:
                                          '${controller.habitStatisticMonthData.value.stages?[1] ?? 0}',
                                      style: TextStyle(
                                          color: primaryColor,
                                          fontSize:
                                              ScreenAdapter.fontSize(52))),
                                  TextSpan(
                                      text: " ${"次".tr}",
                                      style: TextStyle(color: Colors.black87))
                                ]),
                          ),
                          SizedBox(height: ScreenAdapter.height(60)),
                          Text(
                            "阶段二".tr,
                            style: TextStyle(
                              fontSize: ScreenAdapter.fontSize(35),
                              color: Colors.black54,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      )
                  ])));
    }
  }

  Widget _buildHabitTimeline(BuildContext context) {
    return Obx(() {
      var timelines =
          controller.userHabitSnapshotDetailItemModel.value.timeline;
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "时间轴".tr,
                  style: TextStyle(
                      fontSize: ScreenAdapter.fontSize(46),
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF1F2937)),
                ),
                InkWell(
                  onTap: () {
                    showCustomBottomSheet(
                        content: _buildCreateMemoAndReckonChoice(context));
                  },
                  child: Icon(
                    CustomFonts.add,
                    size: ScreenAdapter.width(65),
                    color: primaryColor,
                  ),
                )
              ],
            ),
          ),
          if (timelines == null || timelines.isEmpty)
            _buildEmptyTimelineState(context)
          else
            Container(
              margin: EdgeInsets.only(top: ScreenAdapter.height(10)),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(ScreenAdapter.width(20)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: _buildSimpleTimelineItems(context, timelines),
              ),
            ),
        ],
      );
    });
  }

  // 构建简化版时间轴项目
  List<Widget> _buildSimpleTimelineItems(
      BuildContext context, List<Timeline> timelines) {
    // 按时间从新到旧排序
    timelines.sort((a, b) => b.time!.compareTo(a.time!));

    List<Widget> items = [];

    for (var i = 0; i < timelines.length; i++) {
      var item = timelines[i];
      items.add(_buildSimpleTimelineItem(context, item));
    }

    return items;
  }

  // 构建简化版时间轴条目
  Widget _buildSimpleTimelineItem(BuildContext context, Timeline item) {
    // 获取小时和分钟 - 使用24小时制
    final dateTime = DateTime.fromMillisecondsSinceEpoch(item.time! * 1000);
    final hourStr = dateTime.hour.toString().padLeft(2, '0');
    final minuteStr = dateTime.minute.toString().padLeft(2, '0');

    // 获取活动类型信息和内容
    String tagText;
    Color tagColor;
    String contentText = "";
    bool useBackgroundTag = false;

    switch (item.operateType) {
      case timelineOperatePunch:
        tagText = "打卡";
        tagColor = AppColors.primary.withOpacity(0.9); // 柔和的绿色
        useBackgroundTag = true;
        break;
      case timelineOperateReckon:
        tagText = "计时";
        tagColor = const Color(0xFF4B5563);
        useBackgroundTag = false; // Timer 使用边框样式而不是背景色
        if (item.duration != null) {
          final minutes = (item.duration! / 60).floor();
          final seconds = item.duration! % 60;
          contentText =
              "${"时长: "}${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}";
        }
        break;
      case timelineOperateMemo:
        tagText = "随记";
        tagColor = const Color(0xFFED8936); // 柔和的橙色
        contentText = item.memoContent ?? "";
        break;
      default:
        tagText = "打卡";
        tagColor = AppColors.primary.withOpacity(0.9); // 柔和的绿色
        useBackgroundTag = true;
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧时间
          SizedBox(
            width: 50,
            child: Text(
              "$hourStr:$minuteStr",
              style: const TextStyle(
                fontSize: 13,
                color: Color(0xFF9CA3AF), // 更淡的灰色
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // 右侧内容容器
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                // 去掉所有边框和阴影
              ),
              child: item.operateType == timelineOperateMemo
                  ? _buildNoteItem(
                      context, tagText, tagColor, contentText, item)
                  : _buildStandardItem(context, tagText, tagColor, contentText,
                      useBackgroundTag, item),
            ),
          ),
        ],
      ),
    );
  }

  // 构建标准类型的条目（Check-in和Timer）
  Widget _buildStandardItem(
      BuildContext context,
      String tagText,
      Color tagColor,
      String contentText,
      bool useBackgroundTag,
      Timeline item) {
    return Stack(
      children: [
        // 主要内容
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标签（背景色或边框样式）
            Row(
              children: [
                useBackgroundTag
                    ? Container(
                        // 带背景色的标签（如Check-in）
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: tagColor,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          tagText.tr,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                    : tagText == "计时"
                        ? Container(
                            // 带边框的标签（Timer）
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                  color: const Color(0xFFD1D5DB), width: 0.8),
                            ),
                            child: Text(
                              tagText.tr,
                              style: TextStyle(
                                fontSize: 12,
                                color: tagColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          )
                        : Padding(
                            padding: const EdgeInsets.only(left: 0),
                            child: Text(
                              tagText.tr,
                              style: TextStyle(
                                fontSize: 13,
                                color: tagColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                if (item.smallStageName != null &&
                    item.smallStageName!.isNotEmpty) ...[
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    child: Container(
                      width: 5,
                      height: 5,
                      decoration: BoxDecoration(
                        color: Color(0xFF666666),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  Text(
                    item.smallStageName!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFFFF8C42),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ]
              ],
            ),

            // 内容文本（如果有）
            if (contentText.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8, bottom: 4),
                child: Text(
                  contentText,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF4B5563),
                  ),
                ),
              ),
          ],
        ),

        // 右上角编辑按钮
        Positioned(
          top: 2,
          right: 0,
          child: InkWell(
            onTap: () {
              if (tagText == "计时") {
                // 计时
                showCustomBottomSheet(
                    content: _buildUpdateReckonChoice(context, item));
              } else {
                showCustomBottomSheet(
                    content: _buildUpdatePunchChoice(context, item));
              }
            },
            borderRadius: BorderRadius.circular(20),
            child: Padding(
              padding: const EdgeInsets.all(6),
              child: Icon(
                CustomFonts.edit,
                size: 14,
                color: const Color(0xFF9CA3AF),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建笔记类型的条目
  Widget _buildNoteItem(BuildContext context, String tagText, Color tagColor,
      String contentText, Timeline item) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 左侧橙色线条
        Container(
          width: 3,
          height: contentText.isNotEmpty
              ? contentText.split('\n').length > 2
                  ? 80
                  : 60
              : 30,
          margin: const EdgeInsets.only(top: 10, left: 10),
          decoration: BoxDecoration(
            color: tagColor,
            borderRadius: BorderRadius.circular(1.5),
          ),
        ),

        Expanded(
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标签
                  Row(
                    children: [
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 12, top: 10, bottom: 8),
                        child: Text(
                          tagText.tr,
                          style: TextStyle(
                            fontSize: 13,
                            color: tagColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      if (item.smallStageName != null &&
                          item.smallStageName!.isNotEmpty) ...[
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 10),
                          child: Container(
                            width: 5,
                            height: 5,
                            decoration: BoxDecoration(
                              color: Color(0xFF666666),
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                        Text(
                          item.smallStageName!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFFFF8C42),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ]
                    ],
                  ),

                  // 笔记内容 - 完整显示不截断
                  if (contentText.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 12, right: 16, bottom: 12),
                      child: Text(
                        contentText,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF4B5563),
                          height: 1.4, // 增加行间距
                        ),
                        // 完整显示笔记内容，无行数限制和文本截断
                        maxLines: null,
                        overflow: TextOverflow.visible,
                      ),
                    ),
                ],
              ),

              // 右上角编辑按钮
              Positioned(
                top: 8,
                right: 0,
                child: InkWell(
                  onTap: () {
                    showCustomBottomSheet(
                        content: _buildUpdateMemoChoice(context, item));
                  },
                  borderRadius: BorderRadius.circular(20),
                  child: Padding(
                    padding: const EdgeInsets.all(6),
                    child: Icon(
                      CustomFonts.edit,
                      size: 14,
                      color: const Color(0xFF9CA3AF),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 空状态 - 简化版本，仅保留核心操作按钮
  Widget _buildEmptyTimelineState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 60, horizontal: 20),
      alignment: Alignment.center,
      child: ElevatedButton.icon(
        onPressed: () {
          // 显示添加活动选项
          showCustomBottomSheet(
              content: _buildCreateMemoAndReckonChoice(context));
        },
        icon: Icon(
          Icons.edit_note,
          size: 20,
        ),
        label: Text("添加活动".tr),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: AppColors.primary.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildCreateMemoAndReckonChoice(BuildContext context) {
    return Container(
      width: ScreenAdapter.width(1080),
      height: ScreenAdapter.height(590),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              Get.back();
              HabitNoteDialog.show(
                habitTitle:
                    controller.userHabitSnapshotDetailItemModel.value.name!,
                onSave: (newNote, selectedStageIndex) async {
                  try {
                    // 创建
                    await commonPerformRequest(
                        requestFunction: () => controller.createMemo(newNote,
                            stageIndex: selectedStageIndex));
                  } catch (e) {}
                },
                stageNames: controller
                    .userHabitSnapshotDetailItemModel.value.config!.smallStages,
              );
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "随记".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
          Divider(
            height: ScreenAdapter.height(0),
            color: defaultColor,
          ),
          InkWell(
            onTap: () {
              showCustomBottomSheet(
                  content: _buildCreateOrUpdateReckon("新建计时".tr));
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "计时".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
          Container(
            height: ScreenAdapter.height(20),
            color: defaultColor,
          ),
          InkWell(
            onTap: () {
              Get.back();
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "取消".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdatePunchChoice(BuildContext context, Timeline timelineInfo) {
    return Container(
      width: ScreenAdapter.width(1080),
      height: ScreenAdapter.height(590),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              showCustomBottomSheet(
                  content: _buildUpdatePunch(context, timelineInfo),
                  closeFunction: () {
                    Get.back();
                  });
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "更改打卡".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
          Divider(
            height: ScreenAdapter.height(0),
            color: defaultColor,
          ),
          InkWell(
            onTap: () async {
              if (!(controller.userHabitSnapshotDetailItemModel.value.config
                      ?.isAllowCancelPunch ??
                  false)) {
                showErrorMessage(HiConstants.errorTypeWarning, "不允许取消打卡".tr);
              }
              try {
                await commonPerformRequest(
                    requestFunction: () =>
                        controller.cancelPunch(timelineInfo.punchID!));
                Get.back();
              } catch (e) {}
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "删除".tr,
                style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(48), color: deleteColor),
              ),
            ),
          ),
          Container(
            height: ScreenAdapter.height(20),
            color: defaultColor,
          ),
          InkWell(
            onTap: () {
              Get.back();
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "取消".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateReckonChoice(BuildContext context, Timeline timelineInfo) {
    return Container(
      width: ScreenAdapter.width(1080),
      height: ScreenAdapter.height(590),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              controller.selectedStageIndex.value =
                  timelineInfo.smallStageID ?? 0;
              showCustomBottomSheet(
                  content: _buildCreateOrUpdateReckon("更改计时".tr,
                      timelineInfo: timelineInfo),
                  closeFunction: () {
                    Get.back();
                  });
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "更改计时".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
          Divider(
            height: ScreenAdapter.height(0),
            color: defaultColor,
          ),
          InkWell(
            onTap: () async {
              try {
                await commonPerformRequest(
                    requestFunction: () =>
                        controller.deleteReckon(timelineInfo.reckonID!));
                Get.back();
              } catch (e) {}
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "删除".tr,
                style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(48), color: deleteColor),
              ),
            ),
          ),
          Container(
            height: ScreenAdapter.height(20),
            color: defaultColor,
          ),
          InkWell(
            onTap: () {
              Get.back();
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "取消".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdatePunch(BuildContext context, Timeline timelineInfo) {
    var selectDateTime =
        DateTime.fromMillisecondsSinceEpoch(timelineInfo.time! * 1000)
            .toLocal();
    return SizedBox(
      height: ScreenAdapter.height(1000),
      child: Column(
        children: [
          Expanded(
            child: CupertinoTimerPicker(
              mode: CupertinoTimerPickerMode.hm,
              initialTimerDuration: Duration(
                hours: selectDateTime.hour,
                minutes: selectDateTime.minute,
              ),
              onTimerDurationChanged: (Duration newDuration) {
                selectDateTime = DateTime(
                  selectDateTime.year,
                  selectDateTime.month,
                  selectDateTime.day,
                  newDuration.inHours,
                  newDuration.inMinutes % 60,
                  newDuration.inSeconds % 60,
                );
              },
            ),
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(
                left: ScreenAdapter.width(120),
                top: ScreenAdapter.height(50),
                right: ScreenAdapter.width(120),
                bottom: ScreenAdapter.height(150)),
            child: ElevatedButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(primaryColor),
                foregroundColor: WidgetStateProperty.all(Colors.white),
                shape: WidgetStateProperty.all(RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(ScreenAdapter.height(20)))),
              ),
              onPressed: () async {
                try {
                  await commonPerformRequest(
                      requestFunction: () => controller.updatePunch(
                          timelineInfo.punchID!, selectDateTime));
                  Get.back();
                } catch (e) {}
              },
              child: Text(
                "提交".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreateOrUpdateReckon(String title, {Timeline? timelineInfo}) {
    var hour = 0;
    var minute = 0;
    var second = 0;
    if (timelineInfo != null && timelineInfo.duration != null) {
      var totalSeconds = timelineInfo.duration!;
      hour = totalSeconds ~/ 3600;
      minute = (totalSeconds % 3600) ~/ 60;
      second = totalSeconds % 60;
    }
    var smallStages =
        controller.userHabitSnapshotDetailItemModel.value.config?.smallStages ??
            <String>[];
    return SizedBox(
      height: ScreenAdapter.height(1200),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(40)),
            alignment: Alignment.center,
            child: Text(title,
                style: TextStyle(
                  fontSize: ScreenAdapter.fontSize(50),
                  color: Colors.black87,
                )),
          ),
          if (smallStages.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(
                  left: ScreenAdapter.width(120),
                  top: ScreenAdapter.height(20),
                  right: ScreenAdapter.width(120)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '选择阶段'.tr,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF4B5563),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5FaF8),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: List.generate(
                        smallStages.length,
                        (index) => Expanded(
                          child: GestureDetector(
                            onTap: () {
                              controller.selectedStageIndex.value = index;
                            },
                            child: Obx(() {
                              return Container(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8),
                                decoration: BoxDecoration(
                                  color: controller.selectedStageIndex.value ==
                                          index
                                      ? Colors.white
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(6),
                                  boxShadow: controller
                                              .selectedStageIndex.value ==
                                          index
                                      ? [
                                          BoxShadow(
                                            color:
                                                Colors.black.withOpacity(0.05),
                                            blurRadius: 2,
                                            offset: const Offset(0, 1),
                                          ),
                                        ]
                                      : null,
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  smallStages[index],
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                    overflow: TextOverflow.ellipsis,
                                    color:
                                        controller.selectedStageIndex.value ==
                                                index
                                            ? const Color(0xFF1F2937)
                                            : const Color(0xFF6B7280),
                                  ),
                                ),
                              );
                            }),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 12),
          Expanded(
            child: CupertinoTimerPicker(
              mode: CupertinoTimerPickerMode.hms,
              initialTimerDuration:
                  Duration(hours: hour, minutes: minute, seconds: second),
              onTimerDurationChanged: (Duration newDuration) {
                var totalSeconds = newDuration.inSeconds;
                hour = totalSeconds ~/ 3600;
                minute = (totalSeconds % 3600) ~/ 60;
                second = totalSeconds % 60;
              },
            ),
          ),
          Container(
            width: double.infinity,
            padding: EdgeInsets.only(
                left: ScreenAdapter.width(120),
                top: ScreenAdapter.height(50),
                right: ScreenAdapter.width(120),
                bottom: ScreenAdapter.height(150)),
            child: ElevatedButton(
              style: ButtonStyle(
                backgroundColor: WidgetStateProperty.all(primaryColor),
                foregroundColor: WidgetStateProperty.all(Colors.white),
                shape: WidgetStateProperty.all(RoundedRectangleBorder(
                    borderRadius:
                        BorderRadius.circular(ScreenAdapter.height(20)))),
              ),
              onPressed: () async {
                var duration = hour * 3600 + minute * 60 + second;
                if (duration <= 0) {
                  return;
                }
                try {
                  // 修改
                  if (timelineInfo != null) {
                    await commonPerformRequest(
                        requestFunction: () => controller.updateReckon(
                            duration, timelineInfo.reckonID!));
                  } else {
                    // 创建
                    await commonPerformRequest(
                        requestFunction: () =>
                            controller.createReckon(duration));
                  }
                  Get.back();
                  if (timelineInfo == null) {
                    Get.back();
                  }
                } catch (e) {}
              },
              child: Text(
                "保存".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateMemoChoice(BuildContext context, Timeline timelineInfo) {
    return Container(
      width: ScreenAdapter.width(1080),
      height: ScreenAdapter.height(590),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              Get.back();
              HabitNoteDialog.show(
                habitTitle:
                    controller.userHabitSnapshotDetailItemModel.value.name!,
                initialNote: timelineInfo.memoContent,
                onSave: (newNote, selectedStageIndex) async {
                  try {
                    // 创建
                    await commonPerformRequest(
                        requestFunction: () => controller.updateMemo(
                            newNote, timelineInfo.memoID!,
                            stageIndex: selectedStageIndex));
                  } catch (e) {}
                },
                stageNames: controller
                    .userHabitSnapshotDetailItemModel.value.config!.smallStages,
                selectedStageIndex: controller.selectedStageIndex.value,
              );
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "更改随记".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
          Divider(
            height: ScreenAdapter.height(0),
            color: defaultColor,
          ),
          InkWell(
            onTap: () async {
              try {
                await commonPerformRequest(
                    requestFunction: () =>
                        controller.deleteMemo(timelineInfo.memoID!));
                Get.back();
              } catch (e) {}
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "删除".tr,
                style: TextStyle(
                    fontSize: ScreenAdapter.fontSize(48), color: deleteColor),
              ),
            ),
          ),
          Container(
            height: ScreenAdapter.height(20),
            color: defaultColor,
          ),
          InkWell(
            onTap: () {
              Get.back();
            },
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.only(top: ScreenAdapter.height(36)),
              alignment: Alignment.center,
              child: Text(
                "保存".tr,
                style: TextStyle(fontSize: ScreenAdapter.fontSize(48)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget weekLeftTitles(double value, TitleMeta meta) {
    var style = TextStyle(
      color: Color(0xff7589a2),
      fontWeight: FontWeight.w600,
      fontSize: ScreenAdapter.fontSize(32),
    );
    late String text;

    if (controller.habitStatisticWeekData.value.chartLeftCount!.isNotEmpty) {
      var isNull = true;
      for (var i in controller.habitStatisticWeekData.value.chartLeftCount!) {
        if (value == i) {
          text = '$i';
          isNull = false;
          break;
        }
      }
      if (isNull) {
        return Container();
      }
    } else {
      if (value == 0) {
        text = '0';
      } else if (value == 10) {
        text = '10';
      } else if (value == 20) {
        text = '20';
      } else {
        return Container();
      }
    }

    return SideTitleWidget(
      // 使用最新API，需要传入整个meta对象
      meta: meta,
      space: 0,
      angle: 0,
      child: Text(text, style: style),
    );
  }

  Widget weekBottomTitles(double value, TitleMeta meta) {
    var titles = <String>['一', '二', '三', '四', '五', '六', '日'];
    String locale = Platform.localeName.split('_')[0];
    if (locale != "zh") {
      titles = <String>['1', '2', '3', '4', '5', '6', '7'];
    }

    final Widget text = Text(
      titles[value.toInt()].tr,
      style: TextStyle(
        color: Color(0xff7589a2),
        fontWeight: FontWeight.w600,
        fontSize: ScreenAdapter.fontSize(36),
      ),
    );

    return SideTitleWidget(
      meta: meta,
      space: 4, //margin top
      angle: 0,
      child: text,
    );
  }

  FlTitlesData monthTitlesData() => FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 42,
            interval: 1,
            getTitlesWidget: monthBottomTitleWidgets,
          ),
        ),
        rightTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        topTitles: const AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            getTitlesWidget: monthLeftTitleWidgets,
            showTitles: true,
            interval: 1,
          ),
        ),
      );

  Widget monthLeftTitleWidgets(double value, TitleMeta meta) {
    var style = TextStyle(
      color: Color(0xff7589a2),
      fontWeight: FontWeight.w600,
      fontSize: ScreenAdapter.fontSize(32),
    );
    late String text;
    if (controller.habitStatisticMonthData.value.chartLeftCount!.isNotEmpty) {
      var isNull = true;
      for (var i in controller.habitStatisticMonthData.value.chartLeftCount!) {
        if (value == i) {
          text = '$i';
          isNull = false;
          break;
        }
      }
      if (isNull) {
        return Container();
      }
    } else {
      switch (value.toInt()) {
        case 1:
          text = '1';
          break;
        case 3:
          text = '3';
          break;
        case 5:
          text = '5';
        default:
          return Container();
      }
    }

    return Text(text, style: style, textAlign: TextAlign.center);
  }

  Widget monthBottomTitleWidgets(double value, TitleMeta meta) {
    var style = TextStyle(
      color: Color(0xff7589a2),
      fontWeight: FontWeight.w600,
      fontSize: ScreenAdapter.fontSize(36),
    );
    Widget text;
    switch (value.toInt()) {
      case 1:
        text = Text('1', style: style);
        break;
      case 8:
        text = Text('8', style: style);
        break;
      case 15:
        text = Text('15', style: style);
        break;
      case 23:
        text = Text('23', style: style);
        break;
      case 31:
        text = Text('31', style: style);
        break;
      default:
        text = const Text('');
        break;
    }

    return SideTitleWidget(
      meta: meta,
      space: 4,
      angle: 0,
      child: text,
    );
  }
}
