import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:life_habit_app/app/data/dao/buddy_dao.dart';
import 'package:life_habit_app/app/utils/hi_net.dart';
import 'package:table_calendar/table_calendar.dart';

import '../../../../data/dao/user_habit_dao.dart';

import '../../../../theme/app_colors.dart';
import '../../../../utils/enums.dart';
import '../../../../utils/toast.dart';
import '../../../../utils/tool.dart';
import '../../../../models/buddy_model.dart';
import '../../../home/<USER>/home_controller.dart';
import '../../../discovery/controllers/discovery_controller.dart';
import '../../../../utils/hi_constants.dart';
import '../../../../utils/logger.dart';
import '../../../../utils/bottom_sheet.dart';
import '../../../../widget/reminder_time_picker_widget.dart';

import '../../../../widget/buddy_search_bottom_sheet.dart';

class HabitAddController extends GetxController {
  // Form controllers
  final habitNameController = TextEditingController();
  final stage1Controller = TextEditingController();
  final stage2Controller = TextEditingController();

  // 模板数据和弹窗状态
  TemplateData? templateData;
  final RxBool showScientificDialog = false.obs;

  // Form values
  var habitName = ''.obs;
  var habitType = habitTypeNormal.obs; // Default type is "Regular"
  var stage1 = ''.obs;
  var stage2 = ''.obs;
  var cycle = habitPunchCycleFix.obs; // Default cycle is "Daily"
  var record = recordTypeTime.obs;

  // Selected days (Monday to Sunday)
  var selectedDays = <int>[].obs;
  var availableDays = ['日', '一', '二', '三', '四', '五', '六'];
  var dayIntMap =  <String, int>{
    '日': 0,
    '一': 1,
    '二': 2,
    '三': 3,
    '四': 4,
    '五': 5,
    '六': 6,
  };

  // Daily check-in count
  var checkInTimes = 1.obs;

  // Form submission state
  var isSubmitting = false.obs;
  var formHasErrors = false.obs;
  var errorMessages = <String>[].obs;

  // Controller properties
  final weeklyCheckTimes = 1.obs;
  final monthlyCheckTimes = 1.obs;

  // Controller properties
  final hasEndDate = false.obs; // Whether to enable end date
  final endDate = Rx<DateTime?>(null); // End date value
  final endDateController = ValueNotifier<bool>(false);

  // Strict mode control button
  final strictModeSwitch = false.obs;
  final strictModeController = ValueNotifier<bool>(false);

  // Reward control button
  final rewardSwitch = false.obs;
  final rewardController = ValueNotifier<bool>(false);

  // Advanced options expansion state
  final showAdvancedOptions = false.obs;

  // privacy control button
  final privacySwitch = false.obs;
  final privacyController = ValueNotifier<bool>(false);
  final privacyDisplayType = PrivacyDisplayType.stars.obs;
  final customPrivacyNameController = TextEditingController();

  // 多提醒支持
  final reminderSwitch = false.obs;
  final reminderController = ValueNotifier<bool>(false);
  final reminderTimes = <String>[].obs; // 存储时间字符串，格式："09:30"
  static const int maxReminders = 3; // 最多3个提醒

  // 通知权限检查
  final hasNotificationPermission = false.obs;

  // 搭子功能相关
  final selectedBuddies = <String>[].obs; // 存储选中的搭子用户ID
  final selectedBuddyUsers = <BuddyUser>[].obs; // 存储选中的搭子用户详细信息

  @override
  void onInit() {
    super.onInit();

    // 处理从 discovery 页面传递的模板数据
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      templateData = arguments['template'] as TemplateData?;
      final shouldShowDialog = arguments['showScientificDialog'] as bool? ?? false;

      // 如果有模板数据，预填充表单
      if (templateData != null) {
        _prefillFormFromTemplate(templateData!);

        // 如果需要显示弹窗，延迟500毫秒后显示
        if (shouldShowDialog) {
          Future.delayed(const Duration(milliseconds: 500), () {
            showScientificDialog.value = true;

            // 10秒后自动关闭
            Future.delayed(const Duration(seconds: 10), () {
              hideScientificDialog();
            });
          });
        }
      }
    }
    
    // Default select all weekdays
    selectedDays.value = [1, 2, 3, 4, 5];

    // Listen for form field changes
    habitNameController.addListener(() {
      habitName.value = habitNameController.text;
    });

    stage1Controller.addListener(() {
      stage1.value = stage1Controller.text;
    });

    stage2Controller.addListener(() {
      stage2.value = stage2Controller.text;
    });

    endDateController.addListener(() {
      hasEndDate.value = endDateController.value;
    });

    reminderController.addListener(() {
      reminderSwitch.value = reminderController.value;
    });
  }

  @override
  void onClose() {
    habitNameController.dispose();
    stage1Controller.dispose();
    stage2Controller.dispose();
    endDateController.dispose();
    strictModeController.dispose();
    rewardController.dispose();
    privacyController.dispose();
    reminderController.dispose();
    customPrivacyNameController.dispose();
    super.onClose();
  }

  // Toggle habit type
  void setHabitType(int type) {
    habitType.value = type;
  }

  // Toggle cycle
  void setCycle(int newCycle) {
    cycle.value = newCycle;
  }

  void setRecord(int newRecord) {
    record.value = newRecord;
  }

  // Toggle date selection
  void toggleDay(int day) {
    if (selectedDays.contains(day)) {
      selectedDays.remove(day);
    } else {
      selectedDays.add(day);
    }
  }

  // Increment daily check-in count
  void incrementCheckInTimes() {
    checkInTimes.value++;
  }

  // Decrement daily check-in count (minimum 1)
  void decrementCheckInTimes() {
    if (checkInTimes.value > 1) {
      checkInTimes.value--;
    }
  }

  // Toggle irreversible check-ins option
  void toggleStrictMode() {
    strictModeSwitch.value = !strictModeSwitch.value;
    // 同步更新 strictModeController，确保 UI 状态一致
    strictModeController.value = strictModeSwitch.value;
  }

  // Toggle rewards and motivation option
  void toggleRewardsAndMotivation() {
    rewardSwitch.value = !rewardSwitch.value;
    // 同步更新 rewardController，确保 UI 状态一致
    rewardController.value = rewardSwitch.value;
  }

  /// 切换高级选项展开状态
  void toggleAdvancedOptions() {
    showAdvancedOptions.value = !showAdvancedOptions.value;
  }

  void togglePrivacy() {
    privacySwitch.value = !privacySwitch.value;
    // 同步更新 privacyController，确保 UI 状态一致
    privacyController.value = privacySwitch.value;
  }

  void updatePrivacyDisplayType(PrivacyDisplayType? type) {
    if (type != null) {
      privacyDisplayType.value = type;
    }
  }



  /// 添加新的提醒时间
  void addReminderTime(String timeString) {
    if (reminderTimes.length < maxReminders) {
      reminderTimes.add(timeString);
      reminderSwitch.value = true;
      reminderController.value = true;
      Get.back(); // 关闭弹窗
      Logger.info('新提醒已添加: $timeString');
    }
  }

  /// 删除指定的提醒
  void removeReminderTime(int index) {
    if (index >= 0 && index < reminderTimes.length) {
      final removedTime = reminderTimes.removeAt(index);
      Logger.info('提醒已删除: $removedTime');

      // 如果没有提醒了，关闭提醒开关
      if (reminderTimes.isEmpty) {
        reminderSwitch.value = false;
        reminderController.value = false;
      }
    }
  }

  /// 显示添加提醒的底部弹窗
  void showAddReminderBottomSheet() {
    if (reminderTimes.length >= maxReminders) {
      showErrorMessage(HiConstants.errorTypeWarning, '最多只能添加$maxReminders个提醒'.tr);
      return;
    }

    showCustomBottomSheet(
      content: ReminderTimePickerWidget(
        onTimeSelected: addReminderTime,
        onCancel: () {
          // 用户取消设置，确保开关回到关闭状态
          if (reminderTimes.isEmpty) {
            reminderSwitch.value = false;
            reminderController.value = false;
          }
          Get.back();
        },
      ),
      backgroundColor: Colors.white,
      barrierColor: Colors.black.withOpacity(0.5),
    );
  }

  /// 检查是否可以添加更多提醒
  bool canAddMoreReminders() {
    return reminderTimes.length < maxReminders;
  }

  /// 显示搭子搜索底部弹窗
  void showBuddySearchBottomSheet() {
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      enableDrag: true,
      isDismissible: true,
      useSafeArea: true,
      builder: (context) => AnimatedPadding(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: BuddySearchBottomSheet(
          onBuddySelected: (buddyId) {
            addBuddyToSelected(buddyId);
          },
        ),
      ),
    );
  }

  /// 添加搭子到选中列表
  Future<void> addBuddyToSelected(String buddyId) async {
    if (selectedBuddies.contains(buddyId)) {
      return;
    }

    try {
      // 获取用户详细信息
      final buddyUser = await BuddyDao.getUserById(buddyId);
      if (buddyUser != null) {
        // 设置状态为等待回应（因为刚发送邀请）
        final buddyUserWithStatus = buddyUser.copyWith(status: BuddyStatus.pending);
        selectedBuddies.add(buddyId);
        selectedBuddyUsers.add(buddyUserWithStatus);
      }
    } catch (e) {
      showErrorMessage(HiConstants.errorTypeOther, '获取用户信息失败'.tr);
    }
  }

  /// 移除搭子
  void removeBuddy(String buddyId) {
    selectedBuddies.remove(buddyId);
    selectedBuddyUsers.removeWhere((user) => user.id == buddyId);
  }

  /// 获取搭子用户信息
  BuddyUser? getBuddyUser(String buddyId) {
    try {
      return selectedBuddyUsers.firstWhere((user) => user.id == buddyId);
    } catch (e) {
      return null;
    }
  }

  // Validate form
  bool validateForm() {
    errorMessages.clear();

    // Validate habit name
    if (habitName.value.trim().isEmpty) {
      errorMessages.add('请输入习惯名称');
    }

    // Validate stage 1
    if (habitType.value == habitTypeSmall && (stage1.value.trim().isEmpty || stage2.value.trim().isEmpty)) {
      errorMessages.add('请输入阶段内容');
    }

    if (hasEndDate.value == true && endDate.value == null) {
      errorMessages.add('请选择结束日期');
    }

    if (privacySwitch.value == true && privacyDisplayType.value == PrivacyDisplayType.custom && customPrivacyNameController.text.isEmpty) {
      errorMessages.add('请输入隐私保护中的显示名称');
    }

    formHasErrors.value = errorMessages.isNotEmpty;
    return !formHasErrors.value;
  }

  // Submit form
  Future<void> submitForm() async {
    if (!validateForm()) {
      return;
    }

    isSubmitting.value = true;

    List<int> punchCycle = selectedDays;
    if (cycle.value == habitPunchCycleWeek) {
      punchCycle = [weeklyCheckTimes.value];
    } else if (cycle.value == habitPunchCycleMonth) {
      punchCycle = [monthlyCheckTimes.value];
    }
    var smallStages = habitType.value == habitTypeSmall ? [stage1.value, stage2.value] : [];
    String currentDate = getLocalTime();
    var isAllowCancelPunch = !strictModeSwitch.value;
    if (habitType.value == habitTypeRecord) {
      smallStages = [];
    }
    final timezoneInfo = getLocalTimezone();

    var privacyDisplayMode = "";
    if (privacySwitch.value) {
      privacyDisplayMode = privacyDisplayType.value ==
          PrivacyDisplayType.custom ? "custom" : "mask";
    }

    try {
      await commonPerformRequest(requestFunction: () async {
        await UserHabitsDao.create(<String, Object>{
          "name": habitNameController.text,
          "habit_type": habitType.value,
          "create_date": currentDate,
          "timezone_place": timezoneInfo["timezone_place"]!,
          "timezone": timezoneInfo["timezone"]!,
          "config": {
            "punch_max_count": checkInTimes.value,
            "punch_cycle_type": cycle.value,
            "punch_cycle": punchCycle,
            "small_stages": smallStages,
            "is_allow_cancel_punch": isAllowCancelPunch,
            "is_join_award": rewardSwitch.value,
            "is_set_privacy": privacySwitch.value,
            "privacy_display_mode": privacyDisplayMode,
            "privacy_display_content": customPrivacyNameController.text,
            "record_type": record.value,
            "end_date": endDate.value != null ? DateFormat("yyyy-MM-dd").format(endDate.value!) : "",
            "reminder_times": reminderSwitch.value && reminderTimes.isNotEmpty ? reminderTimes.toList() : [],
            "buddy_list": selectedBuddies.toList(),
          }
        });

        if (Get.isRegistered<HomeController>()) {
          HomeController homeController = Get.find<HomeController>();
          homeController.refreshHabitData();
        }

        Get.back(result: true);

        showSuccessToast("${"新习惯开始养成啦！".tr} 🎉");
      });
    } catch (e) {
      Logger.error("error: $e");
    } finally {
      isSubmitting.value = false;
    }
  }

  // Cancel creation
  void cancelCreation() {
    Get.back();
  }

  void incrementWeeklyTimes() {
    if (weeklyCheckTimes.value < 10) {
      weeklyCheckTimes.value++;
    }
  }

  void decrementWeeklyTimes() {
    if (weeklyCheckTimes.value > 1) {
      weeklyCheckTimes.value--;
    }
  }

  void incrementMonthlyTimes() {
    if (monthlyCheckTimes.value < 10) {
      monthlyCheckTimes.value++;
    }
  }

  void decrementMonthlyTimes() {
    if (monthlyCheckTimes.value > 1) {
      monthlyCheckTimes.value--;
    }
  }

  void showCustomDatePicker() {
    DateTime focusedDay = endDate.value ?? DateTime.now();
    DateTime? selectedDay = endDate.value;
    String locale = Platform.localeName;

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          height: 370,
          padding: const EdgeInsets.all(16),
          child: TableCalendar(
            locale: locale,
            firstDay: DateTime.now(),
            lastDay: DateTime(DateTime.now().year + 5),
            focusedDay: focusedDay,
            selectedDayPredicate: (day) {
              return isSameDay(selectedDay, day);
            },
            onDaySelected: (selected, focused) {
              selectedDay = selected;
              focusedDay = focused;
              endDate.value = selected;
              Get.back();
            },
            headerStyle: HeaderStyle(
              titleCentered: true,
              formatButtonVisible: false,
              titleTextStyle: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF464646),
              ),
              leftChevronIcon:
              Icon(Icons.chevron_left, color: Color(0xFF757575), size: 26,),
              rightChevronIcon:
              Icon(Icons.chevron_right, color: Color(0xFF757575), size: 26,),
            ),
            calendarStyle: CalendarStyle(
              outsideDaysVisible: true,
              cellMargin: EdgeInsets.all(2),
              // 设置单元格外边距
              cellPadding: EdgeInsets.zero,
              // 设置单元格内边距
              rowDecoration: BoxDecoration(
                // 设置行装饰
                color: Colors.transparent,
              ),
              tablePadding: EdgeInsets.zero,
              // 设置整个表格的内边距
              // 设置正常日期的样式
              defaultDecoration: BoxDecoration(
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(6),
                color: Colors.transparent,
              ),
              defaultTextStyle: TextStyle(
                color: Color(0xFF464646),
                fontSize: 16,
              ),
              selectedDecoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6),
                color: AppColors.primary,
                shape: BoxShape.rectangle,
                border: Border.all(color: AppColors.primary, width: 1),
              ),
              selectedTextStyle: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.white,
                fontSize: 16,
              ),
              // 设置今天的样式
              todayDecoration: BoxDecoration(
                color: Colors.transparent,
              ),
              todayTextStyle: TextStyle(
                color: Color(0xFF464646),
                fontSize: 16,
              ),
              // 设置禁用日期的样式（如过去的日期）
              disabledDecoration: BoxDecoration(
                color: Colors.transparent,
              ),
              disabledTextStyle: TextStyle(
                color: Color(0xFF999999), // 更淡的灰色表示禁用状态
                fontSize: 16,
              ),
                outsideTextStyle: TextStyle(
                  color: Color(0xFF464646),
                  fontSize: 16,
                ),
                holidayTextStyle: TextStyle(
                  color: Color(0xFF464646),
                  fontSize: 16,
                ),
                weekendTextStyle: TextStyle(
                  color: Color(0xFF464646),
                  fontSize: 16,
                ),
            ),
            daysOfWeekHeight: 30,
            // 星期栏高度
            rowHeight: 40, // 每行的固定高度
            sixWeekMonthsEnforced: true,
          ),
        ),
      ),
    );
  }

  // 根据模板数据预填充表单
  void _prefillFormFromTemplate(TemplateData template) {
    // 设置习惯名称
    habitNameController.text = template.title;
    habitName.value = template.title;
    
    // 根据模板设置习惯类型为微习惯
    habitType.value = habitTypeSmall;
    
    // 使用模板的双阶段目标
    stage1Controller.text = template.minimumStep;
    stage1.value = template.minimumStep;
    stage2Controller.text = template.stretchGoal;
    stage2.value = template.stretchGoal;
    
    // 使用模板推荐的打卡周期
    cycle.value = habitPunchCycleFix; // 固定周期
    selectedDays.value = List<int>.from(template.recommendedDays);
    
    // 使用模板推荐的每日频率
    checkInTimes.value = template.dailyFrequency;
    
    // 根据模板建议设置一些默认选项
    rewardSwitch.value = true; // 开启奖励
    rewardController.value = true;
  }
  
  // 隐藏科学依据弹窗
  void hideScientificDialog() {
    showScientificDialog.value = false;
  }
}
