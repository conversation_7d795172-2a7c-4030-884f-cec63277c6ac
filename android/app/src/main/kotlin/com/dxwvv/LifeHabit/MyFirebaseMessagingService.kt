package com.dxwvv.LifeHabit

import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import android.util.Log

class MyFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private const val TAG = "FCMService"
    }

    /**
     * Called when message is received.
     *
     * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        // Handle FCM messages here.
        Log.d(TAG, "From: ${remoteMessage.from}")

        // Check if message contains a data payload.
        if (remoteMessage.data.isNotEmpty()) {
            Log.d(TAG, "Message data payload: ${remoteMessage.data}")
            handleDataPayload(remoteMessage.data)
        }

        // Check if message contains a notification payload.
        remoteMessage.notification?.let {
            Log.d(TAG, "Message Notification Body: ${it.body}")
            handleNotificationPayload(it.title, it.body)
        }
    }

    /**
     * Called if the FCM registration token is updated. This may occur if the security of
     * the previous token had been compromised. Note that this is called when the
     * FCM registration token is initially generated so this is where you would retrieve the token.
     */
    override fun onNewToken(token: String) {
        Log.d(TAG, "Refreshed token: $token")
        sendRegistrationToServer(token)
    }

    private fun handleDataPayload(data: Map<String, String>) {
        // Handle data payload here
        Log.d(TAG, "Handling data payload: $data")
        
        // 可以在这里处理自定义数据
        val habitId = data["habit_id"]
        val type = data["type"]
        
        when (type) {
            "habit_reminder" -> {
                // 处理习惯提醒
                Log.d(TAG, "Habit reminder for ID: $habitId")
            }
            "evening_summary" -> {
                // 处理晚间汇总
                Log.d(TAG, "Evening summary notification")
            }
        }
    }

    private fun handleNotificationPayload(title: String?, body: String?) {
        // Handle notification payload here
        Log.d(TAG, "Notification - Title: $title, Body: $body")
        
        // 这里可以创建自定义通知或者让系统自动显示
        // 如果需要自定义通知样式，可以在这里实现
    }

    private fun sendRegistrationToServer(token: String) {
        // Send token to your app server
        Log.d(TAG, "Sending token to server: $token")
        
        // TODO: 实现向服务器发送token的逻辑
        // 这里应该调用你的API来保存用户的FCM token
    }
} 